import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:get/get.dart';
import 'fullscreen_video_player.dart';

class TrailerPlayer extends StatefulWidget {
  final String? trailerUrl;
  final String movieTitle;

  const TrailerPlayer({
    Key? key,
    required this.trailerUrl,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<TrailerPlayer> createState() => _TrailerPlayerState();
}

class _TrailerPlayerState extends State<TrailerPlayer> {
  // Media Kit player và controller
  Player? _player;
  VideoController? _videoController;

  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  bool _isPlaying = false;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      _setError('Không có trailer cho phim này');
      return;
    }

    // Kiểm tra nếu player đã được khởi tạo
    if (_player != null) {
      return;
    }

    try {
      // Khởi tạo player
      _player = Player();
      _videoController = VideoController(_player!);

      // Lắng nghe trạng thái player
      _player!.stream.playing.listen((playing) {
        if (mounted) {
          setState(() {
            _isPlaying = playing;
          });
        }
      });

      _player!.stream.buffering.listen((buffering) {
        if (mounted) {
          setState(() {
            _isLoading = buffering;
          });
        }
      });

      // Xử lý lỗi
      _player!.stream.error.listen((error) {
        if (mounted) {
          _setError('Lỗi phát video: ${error.toString()}');
        }
      });
    } catch (e) {
      _setError('Lỗi khởi tạo player: ${e.toString()}');
    }
  }

  Future<void> _playTrailer() async {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      _setError('Không có URL trailer');
      return;
    }

    // Khởi tạo player nếu chưa có
    if (_player == null) {
      _initializePlayer();
    }

    if (_player == null) {
      _setError('Không thể khởi tạo player');
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Chuyển đổi YouTube URL thành direct video URL nếu cần
      String videoUrl = _processVideoUrl(widget.trailerUrl!);

      print('TrailerPlayer: Loading video URL: $videoUrl');

      // Mở video trong player
      await _player!.open(Media(videoUrl));

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('TrailerPlayer: Error loading video: $e');
      _setError('Lỗi khi tải trailer: ${e.toString()}');
    }
  }

  String _processVideoUrl(String url) {
    // Xử lý YouTube URLs để có thể phát trực tiếp
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      // Đối với YouTube, giữ nguyên URL - media_kit có thể xử lý
      return url;
    }
    return url;
  }

  void _togglePlayPause() {
    if (_player == null) return;

    if (_isPlaying) {
      _player!.pause();
    } else {
      _player!.play();
    }
    _hideControlsAfterDelay();
  }

  void _toggleFullscreen() {
    if (_player == null || _videoController == null) return;

    // Mở fullscreen player trong page mới
    Get.to(
      () => FullscreenVideoPlayer(
        player: _player!,
        videoController: _videoController!,
        movieTitle: widget.movieTitle,
      ),
      transition: Transition.fade,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _setError(String message) {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = message;
      });
    }
  }

  @override
  void dispose() {
    // Đảm bảo quay lại portrait mode khi dispose
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // Dispose player
    _player?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if no trailer URL
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trailer',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildPlayerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerContent() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Đang tải trailer...',
              style: GoogleFonts.mulish(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white54,
              size: 32,
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                _errorMessage ?? 'Không thể phát trailer',
                style: GoogleFonts.mulish(
                  color: Colors.white54,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 4),
            ElevatedButton(
              onPressed: () {
                _initializePlayer();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minimumSize: const Size(0, 32),
              ),
              child: const Text('Thử lại', style: TextStyle(fontSize: 12)),
            ),
          ],
        ),
      );
    }

    // Nếu video đã được load, hiển thị video player
    if (_player != null && _player!.state.playlist.medias.isNotEmpty) {
      return _buildVideoPlayer();
    }

    // Show trailer button (chưa load video)
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 64,
            color: Colors.white.withOpacity(0.8),
          ),
          const SizedBox(height: 12),
          Text(
            'Xem Trailer',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.movieTitle,
            style: GoogleFonts.mulish(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _playTrailer,
            icon: const Icon(Icons.play_arrow, size: 20),
            label: const Text('Phát Trailer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_videoController == null) {
      return const Center(
        child: Text('Video player không khả dụng'),
      );
    }

    return GestureDetector(
      onTap: _toggleControls,
      child: Stack(
        children: [
          // Video player
          Video(
            controller: _videoController!,
            controls: NoVideoControls,
          ),

          // Custom controls overlay
          if (_showControls) _buildCustomControls(),
        ],
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  Widget _buildCustomControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // Top controls
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.movieTitle,
                    style: GoogleFonts.mulish(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  onPressed: _toggleFullscreen,
                  icon: const Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Center play/pause button
          Center(
            child: IconButton(
              onPressed: _togglePlayPause,
              icon: Icon(
                _isPlaying
                    ? Icons.pause_circle_filled
                    : Icons.play_circle_filled,
                size: 64,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),

          const Spacer(),

          // Bottom controls
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
