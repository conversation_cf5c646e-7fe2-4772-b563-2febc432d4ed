import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:get/get.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart' as yt;
import 'fullscreen_video_player.dart';

class TrailerPlayer extends StatefulWidget {
  final String? trailerUrl;
  final String movieTitle;

  const TrailerPlayer({
    Key? key,
    required this.trailerUrl,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<TrailerPlayer> createState() => _TrailerPlayerState();
}

class _TrailerPlayerState extends State<TrailerPlayer> {
  // Media Kit player và controller
  Player? _player;
  VideoController? _videoController;

  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  bool _isPlaying = false;
  bool _showControls = true;

  // Video controls state
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      _setError('Không có trailer cho phim này');
      return;
    }

    // Kiểm tra nếu player đã được khởi tạo
    if (_player != null) {
      return;
    }

    try {
      // Khởi tạo player
      _player = Player();
      _videoController = VideoController(_player!);

      // Lắng nghe trạng thái player
      _player!.stream.playing.listen((playing) {
        if (mounted) {
          setState(() {
            _isPlaying = playing;
          });
        }
      });

      _player!.stream.buffering.listen((buffering) {
        if (mounted) {
          setState(() {
            _isLoading = buffering;
          });
        }
      });

      // Lắng nghe duration
      _player!.stream.duration.listen((duration) {
        if (mounted) {
          setState(() {
            _duration = duration;
          });
        }
      });

      // Lắng nghe position
      _player!.stream.position.listen((position) {
        if (mounted && !_isDragging) {
          setState(() {
            _position = position;
          });
        }
      });

      // Lắng nghe volume
      _player!.stream.volume.listen((volume) {
        if (mounted) {
          setState(() {
            _volume = volume / 100.0; // Media kit volume is 0-100
          });
        }
      });

      // Xử lý lỗi
      _player!.stream.error.listen((error) {
        if (mounted) {
          _setError('Lỗi phát video: ${error.toString()}');
        }
      });
    } catch (e) {
      _setError('Lỗi khởi tạo player: ${e.toString()}');
    }
  }

  Future<void> _playTrailer() async {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      _setError('Không có URL trailer');
      return;
    }

    // Khởi tạo player nếu chưa có
    if (_player == null) {
      _initializePlayer();
    }

    if (_player == null) {
      _setError('Không thể khởi tạo player');
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Chuyển đổi YouTube URL thành direct video URL nếu cần
      String videoUrl = await _processVideoUrl(widget.trailerUrl!);

      print('TrailerPlayer: Loading video URL: $videoUrl');

      // Mở video trong player
      await _player!.open(Media(videoUrl));

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('TrailerPlayer: Error loading video: $e');
      _setError('Lỗi khi tải trailer: ${e.toString()}');
    }
  }

  Future<String> _processVideoUrl(String url) async {
    // Xử lý YouTube URLs - extract direct video URL
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      try {
        final youtubeExplode = yt.YoutubeExplode();
        final video = await youtubeExplode.videos.get(url);
        final manifest =
            await youtubeExplode.videos.streamsClient.getManifest(video.id);

        print(
            'TrailerPlayer: Available streams - Muxed: ${manifest.muxed.length}, VideoOnly: ${manifest.videoOnly.length}, AudioOnly: ${manifest.audioOnly.length}');

        // Thử lấy muxed stream trước
        if (manifest.muxed.isNotEmpty) {
          final streamInfo = manifest.muxed.withHighestBitrate();
          youtubeExplode.close();
          print(
              'TrailerPlayer: Using muxed stream: ${streamInfo.qualityLabel}');
          return streamInfo.url.toString();
        }

        // Fallback: lấy video-only stream với chất lượng thấp hơn
        if (manifest.videoOnly.isNotEmpty) {
          final videoStreams = manifest.videoOnly
              .where((s) => s.container.name == 'mp4')
              .toList();
          if (videoStreams.isNotEmpty) {
            final videoStream = videoStreams.first;
            youtubeExplode.close();
            print(
                'TrailerPlayer: Using video-only stream: ${videoStream.qualityLabel}');
            return videoStream.url.toString();
          }
        }

        youtubeExplode.close();
        throw Exception('Không tìm thấy stream phù hợp');
      } catch (e) {
        print('TrailerPlayer: YouTube extraction error: $e');
        // Fallback: sử dụng video mẫu
        return 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
      }
    }
    return url;
  }

  void _togglePlayPause() {
    if (_player == null) return;

    if (_isPlaying) {
      _player!.pause();
    } else {
      _player!.play();
    }
    _hideControlsAfterDelay();
  }

  void _seekTo(Duration position) {
    if (_player == null) return;
    _player!.seek(position);
  }

  void _setVolume(double volume) {
    if (_player == null) return;
    setState(() {
      _volume = volume;
    });
    _player!.setVolume(volume * 100); // Media kit volume is 0-100
  }

  void _setPlaybackSpeed(double speed) {
    if (_player == null) return;
    setState(() {
      _playbackSpeed = speed;
    });
    _player!.setRate(speed);
  }

  void _onSeekStart() {
    setState(() {
      _isDragging = true;
    });
  }

  void _onSeekEnd() {
    setState(() {
      _isDragging = false;
    });
  }

  void _toggleFullscreen() {
    if (_player == null || _videoController == null) return;

    // Mở fullscreen player trong page mới
    Get.to(
      () => FullscreenVideoPlayer(
        player: _player!,
        videoController: _videoController!,
        movieTitle: widget.movieTitle,
      ),
      transition: Transition.fade,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _setError(String message) {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = message;
      });
    }
  }

  @override
  void dispose() {
    // Đảm bảo quay lại portrait mode khi dispose
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // Dispose player
    _player?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if no trailer URL
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trailer',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildPlayerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerContent() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Đang tải trailer...',
              style: GoogleFonts.mulish(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white54,
              size: 32,
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                _errorMessage ?? 'Không thể phát trailer',
                style: GoogleFonts.mulish(
                  color: Colors.white54,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 4),
            ElevatedButton(
              onPressed: () {
                _initializePlayer();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minimumSize: const Size(0, 32),
              ),
              child: const Text('Thử lại', style: TextStyle(fontSize: 12)),
            ),
          ],
        ),
      );
    }

    // Nếu video đã được load, hiển thị video player
    if (_player != null && _player!.state.playlist.medias.isNotEmpty) {
      return _buildVideoPlayer();
    }

    // Show trailer button (chưa load video)
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 64,
            color: Colors.white.withOpacity(0.8),
          ),
          const SizedBox(height: 12),
          Text(
            'Xem Trailer',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.movieTitle,
            style: GoogleFonts.mulish(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _playTrailer,
            icon: const Icon(Icons.play_arrow, size: 20),
            label: const Text('Phát Trailer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    if (_videoController == null) {
      return const Center(
        child: Text('Video player không khả dụng'),
      );
    }

    return GestureDetector(
      onTap: _toggleControls,
      child: Stack(
        children: [
          // Video player
          Video(
            controller: _videoController!,
            controls: NoVideoControls,
          ),

          // Custom controls overlay
          if (_showControls) _buildCustomControls(),
        ],
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  Widget _buildCustomControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top controls
          Padding(
            padding: const EdgeInsets.all(4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.movieTitle,
                    style: GoogleFonts.mulish(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Playback speed button
                _buildSpeedButton(),
                // Volume button
                _buildVolumeButton(),
                // Fullscreen button
                IconButton(
                  onPressed: _toggleFullscreen,
                  iconSize: 20,
                  padding: EdgeInsets.zero,
                  constraints:
                      const BoxConstraints(minWidth: 32, minHeight: 32),
                  icon: const Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          Flexible(
            child: Center(
              child: IconButton(
                onPressed: _togglePlayPause,
                icon: Icon(
                  _isPlaying
                      ? Icons.pause_circle_filled
                      : Icons.play_circle_filled,
                  size: 48,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ),
          ),

          // Bottom controls - Progress bar
          _buildProgressBar(),
        ],
      ),
    );
  }

  Widget _buildSpeedButton() {
    return PopupMenuButton<double>(
      icon: const Icon(Icons.speed, color: Colors.white, size: 20),
      iconSize: 20,
      padding: EdgeInsets.zero,
      onSelected: _setPlaybackSpeed,
      itemBuilder: (context) => [
        const PopupMenuItem(value: 0.5, child: Text('0.5x')),
        const PopupMenuItem(value: 0.75, child: Text('0.75x')),
        const PopupMenuItem(value: 1.0, child: Text('1.0x')),
        const PopupMenuItem(value: 1.25, child: Text('1.25x')),
        const PopupMenuItem(value: 1.5, child: Text('1.5x')),
        const PopupMenuItem(value: 2.0, child: Text('2.0x')),
      ],
    );
  }

  Widget _buildVolumeButton() {
    return PopupMenuButton<void>(
      icon: Icon(
        _volume == 0 ? Icons.volume_off : Icons.volume_up,
        color: Colors.white,
        size: 20,
      ),
      iconSize: 20,
      padding: EdgeInsets.zero,
      itemBuilder: (context) => [
        PopupMenuItem(
          enabled: false,
          child: StatefulBuilder(
            builder: (context, setState) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Âm lượng: ${(_volume * 100).round()}%'),
                Slider(
                  value: _volume,
                  onChanged: (value) {
                    setState(() {});
                    _setVolume(value);
                  },
                  min: 0.0,
                  max: 1.0,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.amber,
              inactiveTrackColor: Colors.white.withOpacity(0.3),
              thumbColor: Colors.amber,
              overlayColor: Colors.amber.withOpacity(0.2),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
              trackHeight: 3,
            ),
            child: Slider(
              value: _duration.inMilliseconds > 0
                  ? _position.inMilliseconds / _duration.inMilliseconds
                  : 0.0,
              onChangeStart: (_) => _onSeekStart(),
              onChanged: (value) {
                final newPosition = Duration(
                  milliseconds: (value * _duration.inMilliseconds).round(),
                );
                setState(() {
                  _position = newPosition;
                });
              },
              onChangeEnd: (value) {
                final newPosition = Duration(
                  milliseconds: (value * _duration.inMilliseconds).round(),
                );
                _seekTo(newPosition);
                _onSeekEnd();
              },
            ),
          ),
          // Time labels
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDuration(_position),
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  _formatDuration(_duration),
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
